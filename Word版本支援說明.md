# Word多版本支援說明

## 概述
本程式已更新為支援多個Microsoft Office版本，包括：
- Office 2010 (版本 14.0)
- Office 2013 (版本 15.0)
- Office 2019/2021/365 (版本 16.0)
- Office 2024 (版本 17.0+)

## 新增功能

### 1. 自動版本檢測
- `get_word_version(word_app)`: 自動檢測當前安裝的Word版本
- `get_word_version_name(version_float)`: 根據版本號返回版本名稱

### 2. 版本特定設定
- `setup_word_options(word_app, version)`: 根據版本設定相應選項
- `setup_word2010_options(word_app)`: Word 2010特定設定
- `setup_word_modern_options(word_app)`: Word 2019/2024特定設定
- `setup_word_legacy_options(word_app)`: 舊版本Word設定

### 3. 註冊表設定
- `setup_word_registry(version)`: 根據版本設定註冊表項目
- `get_office_version_registry_path(version)`: 取得版本對應的註冊表路徑

### 4. 現代化處理方式
- `process_word_document_modern()`: 針對現代版本優化的處理函數
- 支援自動恢復禁用、背景儲存禁用等現代功能

## 使用方式

### 基本使用
```bash
python main2.py
```

### 版本檢測測試
```bash
python main2.py test
```

### 使用批次檔案
```bash
test_word_versions.bat
```

## 版本差異處理

### Office 2010
- 註冊表路徑: `Software\Microsoft\Office\14.0\Word\Security`
- 特殊設定: 禁用LivePreview、受保護視圖

### Office 2019/2024
- 註冊表路徑: `Software\Microsoft\Office\16.0\Word\Security` (2019) 或 `17.0` (2024)
- 特殊設定: 禁用雲端同步、自動儲存、智慧查閱
- 額外功能: 禁用自動恢復、背景儲存

### 相容性
- 程式會自動檢測版本並使用相應設定
- 如果現代化處理失敗，會自動回退到傳統處理方式
- 保持與舊版本的向後相容性

## 錯誤處理
- 自動版本檢測失敗時預設使用2010設定
- 現代化處理失敗時自動回退到傳統方式
- 完整的錯誤日誌記錄

## 測試建議
1. 先執行版本檢測測試確認Word版本
2. 檢查日誌檔案確認設定是否正確
3. 測試實際文件處理功能
